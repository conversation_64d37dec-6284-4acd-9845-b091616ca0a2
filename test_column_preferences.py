#!/usr/bin/env python3
"""
Test script to verify column preferences functionality
"""

import json
import requests

# Test data
test_columns = {
    "checkbox": 50,
    "task": 450,
    "owner": 250,
    "status": 200,
    "planned_start_date": 280,
    "planned_end_date": 280,
    "actual_start_date": 280,
    "actual_end_date": 280,
    "remarks": 150,
    "actions": 60
}

def test_save_preferences():
    """Test saving column preferences"""
    url = "http://localhost:8000/api/method/inspira.inspira.api.projects.project_planner.save_column_preferences"
    
    data = {
        "view_name": "Planner",
        "columns": json.dumps(test_columns)
    }
    
    try:
        response = requests.post(url, data=data)
        print(f"Save Response Status: {response.status_code}")
        print(f"Save Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Save Error: {e}")
        return False

def test_load_preferences():
    """Test loading column preferences"""
    url = "http://localhost:8000/api/method/inspira.inspira.api.projects.project_planner.load_column_preferences"
    
    data = {
        "view_name": "Planner"
    }
    
    try:
        response = requests.post(url, data=data)
        print(f"Load Response Status: {response.status_code}")
        result = response.json()
        print(f"Load Response: {result}")
        
        if result.get("message", {}).get("status") == "success":
            loaded_columns = result["message"]["columns"]
            print(f"Loaded columns: {loaded_columns}")
            return loaded_columns == test_columns
        return False
    except Exception as e:
        print(f"Load Error: {e}")
        return False

if __name__ == "__main__":
    print("Testing Column Preferences API...")
    print("=" * 50)
    
    print("\n1. Testing Save Preferences...")
    save_success = test_save_preferences()
    
    print("\n2. Testing Load Preferences...")
    load_success = test_load_preferences()
    
    print("\n" + "=" * 50)
    print(f"Save Test: {'PASSED' if save_success else 'FAILED'}")
    print(f"Load Test: {'PASSED' if load_success else 'FAILED'}")
    print(f"Overall: {'PASSED' if save_success and load_success else 'FAILED'}")
