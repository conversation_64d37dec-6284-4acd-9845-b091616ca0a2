<template>
  <td :class="cellClasses" :style="cellStyle">
    <!-- Checkbox Cell -->
    <template v-if="column.type === 'checkbox'">
      <!-- Background placeholder -->
      <div class="absolute inset-0 bg-white"
        :class="{ 'invisible': selectedTasks.includes(item.id) || isHovered }"></div>

      <!-- Interactive elements -->
      <div class="text-sm py-3 flex items-center gap-2 transition duration-300 relative" :class="{
        'opacity-100 translate-x-0': selectedTasks.includes(item.id),
        'opacity-0 translate-x-1 group-hover:opacity-100 group-hover:translate-x-0': !selectedTasks.includes(item.id),
      }" @mouseover="isHovered = true" @mouseleave="isHovered = false">
        <span class="cursor-move">
          <NineDotsIcon />
        </span>
        <input type="checkbox" class="border rounded-sm bg-[#CAC4D0]" v-model="selectedTasks"
          :value="item.id" :disabled="isSubtask ? hasSelectedMainTasks : hasSelectedSubtasks"
          @mouseover="e => (isSubtask ? hasSelectedMainTasks : hasSelectedSubtasks) && showTooltip(e, isSubtask ? 'Cannot select subtask while main tasks are selected' : 'Cannot select main task while subtasks are selected')"
          @mouseleave="hideTooltip" />
      </div>
    </template>

    <!-- Task Cell -->
    <template v-else-if="column.type === 'task'">
      <div class="flex items-center justify-between cursor-pointer">
        <p class="flex items-center gap-1" :class="{ 'pl-8': isSubtask }">
          <!-- Expand/Collapse icon for main tasks only -->
          <span v-if="!isSubtask" class="transform transition-transform hover:bg-[#e7e7e7] rounded-sm"
            @click="$emit('toggle-expanded')" :class="{ 'rotate-this': !item.isExpanded }">
            <SmallDropdownIcon />
          </span>
          
          <!-- Editable task name -->
          <span v-if="item.isEditing">
            <input v-model="item.name" ref="taskInput" v-focus v-select
              @blur="$emit('blur-editing')"
              @keyup.enter="$emit('finish-editing')"
              class="text-sm px-2 py-1 border-none outline-none bg-transparent"
              :placeholder="isSubtask ? 'Enter Subtask Name' : 'Enter Task Name'" @click.stop />
          </span>
          <span v-else>
            {{ item.name }}
          </span>
          
          <!-- Subtask indicators for main tasks -->
          <template v-if="!isSubtask">
            <span v-if="item.subtasks?.length">
              <LinkageIcon />
            </span>
            <span style="font-size: 11px; color: #79747e" v-if="item.subtasks?.length">
              {{ item.subtasks?.length || 0 }}
            </span>
          </template>
        </p>
        
        <!-- Action buttons -->
        <div v-if="!isEditingSubtask"
          class="py-1 text-sm flex items-center opacity-0 translate-x-1 group-hover:opacity-100 group-hover:translate-x-0 transition duration-300"
          :class="{ 'px-4': !isSubtask }">
          <button v-if="!isSubtask" @click="$emit('add-subtask')"
            class="ml-2 text-gray-400 bg-gray-300 rounded-sm">
            <AddSubTaskIcon />
          </button>
          <button @click.stop="$emit('start-editing')"
            class="ml-2 text-gray-400 bg-gray-300 rounded-sm p-0.5">
            <PencilIcon />
          </button>
        </div>
      </div>
    </template>

    <!-- Autocomplete Cell (Owner, Status) -->
    <template v-else-if="column.type === 'autocomplete'">
      <Autocomplete 
        :options="getOptions(column)" 
        :modelValue="getFieldValue(item, column)" 
        :placeholder="getPlaceholder(column)"
        :multiple="column.multiple" 
        :by="column.multiple ? (a, b) => a.value === b.value : undefined"
        :showOptions="!column.multiple"
        :class="{ 'custom_classes': !column.multiple }"
        @update:modelValue="handleFieldUpdate(column, $event)">
        
        <!-- Owner field template -->
        <template v-if="column.key === 'owner'" #target="{ togglePopover }">
          <div @click="togglePopover" class="cursor-pointer">
            <span v-if="item.assignees?.length" class="flex -space-x-1">
              <div v-for="(assignee, idx) in item.assignees" :key="idx"
                class="w-8 h-8 rounded-full bg-[#CCC2DC] flex items-center justify-center text-xs font-medium text-[#000] ring-1 ring-white">
                <Tooltip :text="assignee.label" :hover-delay="0" :placement="'top'">
                  <span>
                    {{ getInitialsFromFullName(assignee) }}
                  </span>
                </Tooltip>
              </div>
            </span>
            <span v-else
              class="w-8 h-8 rounded-full bg-[#CCC2DC] flex items-center justify-center text-xs font-medium text-[#000] ring-1 ring-white">
              <OwnerIcon />
            </span>
          </div>
        </template>
        
        <!-- Owner field footer -->
        <template v-if="column.key === 'owner'" #footer="{ togglePopover }">
          <div class="flex items-center justify-end">
            <Button variant="solid" @click="handleFieldUpdate(column, item.assignees), togglePopover()">
              Update
            </Button>
          </div>
        </template>
      </Autocomplete>
    </template>

    <!-- Date Cell -->
    <template v-else-if="column.type === 'date'">
      <div class="relative">
        <div v-if="!getFieldValue(item, column)"
          class="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none"
          style="z-index: 5;">
          <CalendarIcon class="h-5 w-5" />
        </div>
        <DatePicker 
          icon-left="calendar" 
          :value="getFieldValue(item, column)" 
          placeholder=""
          :disabled="false" 
          :formatter="(date) => formatDate(date, 'DD-MMM-YYYY')" 
          @change="handleFieldUpdate(column, $event)" />
      </div>
    </template>

    <!-- Text Cell (Remarks) -->
    <template v-else-if="column.type === 'text'">
      <div class="relative">
        <span v-if="column.clickable" 
          class="block truncate cursor-pointer" 
          :style="{ width: column.width + 'px' }"
          @click="handleRemarksClick()">
          {{ getFieldValue(item, column) ?? '-' }}
        </span>
        <span v-else>
          {{ getFieldValue(item, column) ?? '-' }}
        </span>
      </div>
    </template>

    <!-- Actions Cell -->
    <template v-else-if="column.type === 'actions'">
      <!-- Actions content can be added here -->
    </template>
  </td>
</template>

<script setup>
import { ref, computed, inject } from 'vue'
import { 
  Tooltip, 
  Autocomplete, 
  DatePicker, 
  Button 
} from 'frappe-ui'
import { formatDate } from '../utils/format'

// Icons
import SmallDropdownIcon from './icons/SmallDropdownIcon.vue'
import LinkageIcon from './icons/LinkageIcon.vue'
import AddSubTaskIcon from './icons/AddSubTaskIcon.vue'
import PencilIcon from './icons/PencilIcon.vue'
import OwnerIcon from './icons/OwnerIcon.vue'
import NineDotsIcon from './icons/NineDotsIcon.vue'
import CalendarIcon from './icons/CalendarIcon.vue'

const props = defineProps({
  column: {
    type: Object,
    required: true
  },
  item: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    required: true
  },
  taskIndex: {
    type: Number,
    required: true
  },
  subtaskIndex: {
    type: Number,
    default: null
  },
  isSubtask: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits([
  'field-change',
  'toggle-expanded',
  'add-subtask',
  'start-editing',
  'finish-editing',
  'blur-editing',
  'show-remarks'
])

// Inject global state and functions
const selectedTasks = inject('selectedTasks')
const hasSelectedMainTasks = inject('hasSelectedMainTasks')
const hasSelectedSubtasks = inject('hasSelectedSubtasks')
const isEditingSubtask = inject('isEditingSubtask')
const showTooltip = inject('showTooltip')
const hideTooltip = inject('hideTooltip')
const usersLists = inject('usersLists')
const statusList = inject('statusList')
const getInitialsFromFullName = inject('getInitialsFromFullName')

const isHovered = ref(false)

// Computed properties
const cellClasses = computed(() => {
  let classes = 'relative'
  
  if (props.column.type !== 'checkbox' && props.column.type !== 'actions') {
    classes += ' activecell'
  }
  
  return classes
})

const cellStyle = computed(() => {
  let style = 'py-1 text-sm px-4 border-b'
  
  if (props.column.sticky) {
    if (props.column.sticky === 'left') {
      style += ` bg-white group-hover:bg-[#F8F7F9]`
      return `${style}; position: sticky; left: ${props.column.stickyPosition}; z-index: ${props.column.key === 'checkbox' ? '20' : '10'}; background: white;`
    } else if (props.column.sticky === 'right') {
      style += ` bg-white group-hover:bg-[#F8F7F9]`
      return `${style}; position: sticky; right: ${props.column.stickyPosition}; z-index: 10;`
    }
  } else {
    style += ' bg-white group-hover:bg-[#F8F7F9]'
  }
  
  return style
})

// Helper functions
const getFieldValue = (item, column) => {
  return item[column.field] || (column.field === 'assignees' ? item.assignees : null)
}

const getOptions = (column) => {
  if (column.options === 'usersLists') return usersLists.value
  if (column.options === 'statusList') return statusList.value
  return []
}

const getPlaceholder = (column) => {
  if (column.key === 'owner') return 'Select Assignee'
  if (column.key === 'status') return 'Select Status'
  return `Select ${column.label}`
}

const handleFieldUpdate = (column, value) => {
  emit('field-change', column, value, props.index, props.taskIndex, props.subtaskIndex)
}

const handleRemarksClick = () => {
  const remarksData = {
    title: `Remarks for ${props.item.name}`,
    index: props.index,
    taskIndex: props.taskIndex,
    subtaskIndex: props.subtaskIndex,
    remarks: props.item.remarks,
    updatingTaskRemarks: !props.isSubtask,
    updatingSubTaskRemarks: props.isSubtask,
  }
  emit('show-remarks', remarksData)
}
</script>
