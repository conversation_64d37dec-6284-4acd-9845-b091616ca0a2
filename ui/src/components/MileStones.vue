<template>
  <div class="w-full overflow-auto h-[68vh] relative">
    <div v-for="(section, index) in sections" :key="index" class="w-full border-l-4 rounded-md mb-4"
      :style="{ borderColor: section.color }">
      <div class="pt-4 pb-4">
        <!-- Header section -->
        <div class="flex items-center gap-6 cursor-pointer group">
          <div class="flex items-center gap-6 cursor-pointer" @click="toggleSection(index)"
            style="position: sticky;left:10px;">
            <span class="transform transition-transform" :class="{ 'rotate-this': !section.isExpanded }">
              <DropDownIcon />
            </span>
            <input v-if="section.isEditing" v-model="section.name" @blur="handleSectionBlur(index)"
              @keyup.enter="finishEditingSection(index)" ref="sectionNameInput" v-focus v-select
              class="text-sm px-2 py-1 border-none outline-none" :placeholder="'Enter Section Name'" />
            <button v-else style="color: #49454f" class="font-normal">
              {{ section.name }}
            </button>
            <span class="text-gray-500">{{ section.tasks.length }}</span>
          </div>

          <div v-if="!isEditingTask"
            class="flex gap-2 opacity-0 translate-x-1 group-hover:opacity-100 group-hover:translate-x-0 transition duration-300"
            style="position: sticky; left: 250px;">
            <button class="bg-gray-100 rounded-sm p-1" @click="addTask(index)">
              <span>
                <PlusButtonIcon />
              </span>
            </button>
            <CustomDropDown :options="[
              {
                label: 'Rename Section',
                icon: () => h(FeatherIcon, { name: 'edit' }),
                onClick: () => {
                  section.isEditing = true
                  nextTick(() => {
                    sectionNameInput.value.focus()
                  })
                },
              },
              {
                label: 'Add Section',
                icon: () => h(FeatherIcon, { name: 'plus-square' }),
                subItems: [
                  {
                    icon: () => h(FeatherIcon, { name: 'arrow-up' }),
                    label: 'Above',
                    onClick: () => {
                      addNewSection(index)
                    },
                  },
                  {
                    icon: () => h(FeatherIcon, { name: 'arrow-down' }),
                    label: 'Below',
                    onClick: () => {
                      // Since adding below, hence adding 1
                      addNewSection(index + 1)
                    },
                  },
                ],
              },
              {
                label: 'Duplicate Section',
                icon: () => h(FeatherIcon, { name: 'copy' }),
                onClick: () => {
                  duplicateTaskWithChildren([section.id])
                },
              },
              {
                label: 'Delete Section',
                icon: () => h(FeatherIcon, { name: 'trash-2' }),
                onClick: () => {
                  deleteTaskWithChildren([section.id], index)
                },
              },
            ]">
              <button class="bg-gray-100 rounded-sm p-1">
                <ThreeDots />
              </button>
            </CustomDropDown>
          </div>
        </div>

        <!-- Tasks table -->
        <div v-if="section.isExpanded" class="mt-2 w-full relative">
          <div class="table-container">
            <table class="resizable-table">
              <thead>
                <tr class="text-left" style="background: #ece6f0">
                  <ResizableHeader columnName="checkbox" :width="columnWidths.checkbox" style="
                      background-color: white !important;
                      position: sticky;
                      left: 0px;
                      z-index: 20;
                    ">
                    <!-- Empty for checkbox column -->
                  </ResizableHeader>

                  <ResizableHeader columnName="task" label="Task" :width="columnWidths.task" :minWidth="350"
                    :maxWidth="600" @update:width="(width) => (columnWidths.task = width)"
                    style="position: sticky; left: 50px; z-index: 20" class="taskcolumn">
                  </ResizableHeader>

                  <ResizableHeader columnName="owner" label="Owner" :width="columnWidths.owner" :minWidth="150"
                    :maxWidth="400" @update:width="(width) => (columnWidths.owner = width)" />

                  <ResizableHeader columnName="status" label="Status" :width="columnWidths.status" :minWidth="180"
                    :maxWidth="400" @update:width="(width) => (columnWidths.status = width)" />

                  <ResizableHeader columnName="date" label="Planned Start Date" :width="columnWidths.planned_start_date"
                    :minWidth="150" :maxWidth="400" @update:width="
                      (width) => (columnWidths.planned_start_date = width)
                    " />

                  <ResizableHeader columnName="date" label="Planned End Date" :width="columnWidths.planned_end_date"
                    :minWidth="150" :maxWidth="400" @update:width="
                      (width) => (columnWidths.planned_end_date = width)
                    " />

                  <ResizableHeader columnName="date" label="Actual Start Date" :width="columnWidths.actual_start_date"
                    :minWidth="150" :maxWidth="400" @update:width="
                      (width) => (columnWidths.actual_start_date = width)
                    " />

                  <ResizableHeader columnName="date" label="Actual End Date" :width="columnWidths.actual_end_date"
                    :minWidth="150" :maxWidth="400" @update:width="
                      (width) => (columnWidths.actual_end_date = width)
                    " />

                  <ResizableHeader columnName="remarks" label="Remarks" :width="columnWidths.remarks" :minWidth="200"
                    :maxWidth="400" @update:width="(width) => (columnWidths.remarks = width)" />

                  <!-- <ResizableHeader columnName="actions" :width="columnWidths.actions" 
                    @update:width="(width) => (columnWidths.actions = width)">
                    <button class="text-gray-400 hover:text-gray-600"> -->
                  <ResizableHeader columnName="actions" :isLastColumn="true" class="last-column-header"
                    :width="columnWidths.actions">
                    <!-- <button class="text-gray-400 hover:text-gray-600">
                      <PlusButtonIcon />
                    </button> -->
                  </ResizableHeader>
                </tr>
              </thead>
              <tbody class="ManageColorState">
                <template v-for="(task, taskIndex) in section.tasks" :key="taskIndex">
                  <!-- Main task start here -->
                  <tr class="border-t border-gray-100 group hover:bg-[#F8F7F9]" draggable="true"
                    @dragstart="dragStart($event, index, taskIndex)" @dragover.prevent @dragenter.prevent
                    @drop="drop($event, index, taskIndex)" @dragover="dragOver($event, index, taskIndex)"
                    @dragleave="dragLeave($event)">
                    <td class="relative" style="position: sticky; left: 0px; z-index: 20;background: white;">
                      <!-- Background placeholder -->
                      <div class="absolute inset-0 bg-white"
                        :class="{ 'invisible': selectedTasks.includes(task.id) || isHovered }"></div>

                      <!-- Interactive elements -->
                      <div class="text-sm py-3 flex items-center gap-2 transition duration-300 relative" :class="{
                        'opacity-100 translate-x-0': selectedTasks.includes(task.id),
                        'opacity-0 translate-x-1 group-hover:opacity-100 group-hover:translate-x-0': !selectedTasks.includes(task.id),
                      }" @mouseover="isHovered = true" @mouseleave="isHovered = false">
                        <span class="cursor-move">
                          <NineDotsIcon />
                        </span>
                        <input type="checkbox" class="border rounded-sm bg-[#CAC4D0]" v-model="selectedTasks"
                          :value="task.id" :disabled="hasSelectedSubtasks"
                          @mouseover="e => hasSelectedSubtasks && showTooltip(e, 'Cannot select main task while subtasks are selected')"
                          @mouseleave="hideTooltip" />
                      </div>
                    </td>

                    <!--Main Task cell: -->
                    <td class="py-1 text-sm border-b bg-white group-hover:bg-[#F8F7F9]"
                      style="position: sticky; left: 50px; z-index: 10">
                      <div class="flex items-center justify-between cursor-pointer">
                        <p class="flex items-center gap-1">
                          <span class="transform transition-transform hover:bg-[#e7e7e7] rounded-sm"
                            @click="task.isExpanded = !task.isExpanded" :class="{ 'rotate-this': !task.isExpanded }">
                            <SmallDropdownIcon />
                          </span>
                          <span v-if="task.isEditing">
                            <input v-model="task.name" ref="newTaskInput" v-focus v-select
                              @blur="handleTaskBlur(index, taskIndex)"
                              @keyup.enter="finishEditingTask(index, taskIndex)"
                              class="text-sm px-2 py-1 border-none outline-none bg-transparent"
                              placeholder="Enter Task Name" @click.stop />
                          </span>
                          <span v-else>
                            <!-- <Tooltip :text="task.name" :hover-delay="0" :placement="'top'">
                              <span>
                                {{ task.name }}
                              </span>
                            </Tooltip> -->
                            {{ task.name }}
                          </span>
                          <span v-if="task.subtasks?.length">
                            <LinkageIcon />
                          </span>
                          <span style="font-size: 11px; color: #79747e" v-if="task.subtasks?.length">
                            {{ task.subtasks?.length || 0 }}
                          </span>
                        </p>
                        <div v-if="!isEditingSubtask"
                          class="py-1 text-sm px-4 flex items-center opacity-0 translate-x-1 group-hover:opacity-100 group-hover:translate-x-0 transition duration-300">
                          <button @click="addSubtask(index, taskIndex)"
                            class="ml-2 text-gray-400 bg-gray-300 rounded-sm">
                            <AddSubTaskIcon />
                          </button>
                          <button @click.stop="startEditingTaskName(index, taskIndex)"
                            class="ml-2 text-gray-400 bg-gray-300 rounded-sm p-0.5">
                            <PencilIcon />
                          </button>
                        </div>
                      </div>
                    </td>

                    <!-- Task owner cell: -->

                    <td class="py-1 text-sm px-4 relative border-b activecell">
                      <!-- {{ task.owner }} -->
                      <Autocomplete :options="usersLists" v-model="task.assignees" placeholder="Select Assignee"
                        :multiple="true" :by="(a, b) => a.value === b.value">
                        <template #target="{ togglePopover }">
                          <div @click="togglePopover" class="cursor-pointer">
                            <span v-if="task.assignees?.length" class="flex -space-x-1">
                              <div v-for="(assignee, idx) in task.assignees" :key="idx"
                                class="w-8 h-8 rounded-full bg-[#CCC2DC] flex items-center justify-center text-xs font-medium text-[#000] ring-1 ring-white">
                                <Tooltip :text="assignee.label" :hover-delay="0" :placement="'top'">
                                  <span>
                                    {{ getInitialsFromFullName(assignee) }}
                                  </span>
                                </Tooltip>
                              </div>
                            </span>
                            <span v-else
                              class="w-8 h-8 rounded-full bg-[#CCC2DC] flex items-center justify-center text-xs font-medium text-[#000] ring-1 ring-white">
                              <OwnerIcon />
                            </span>
                          </div>
                        </template>
                        <template #footer="{ togglePopover }">
                          <div class="flex items-center justify-end">
                            <Button variant="solid" @click="
                              handleFieldChange(
                                index,
                                taskIndex,
                                'owner',
                                task.assignees,
                                'task_assignee'
                              ),
                              togglePopover()
                              ">Update</Button>
                          </div>
                        </template>
                      </Autocomplete>
                    </td>

                    <!-- status cell: -->
                    <td class="py-1 text-sm px-4 border-b activecell">
                      <Autocomplete ref="autocompleteRef" v-model="task.status" :placeholder="'Select Status'"
                        :showOptions="true" :options="statusList" class="custom_classes" @update:modelValue="handleFieldChange(
                          index,
                          taskIndex,
                          'status',
                          task.status,
                          'status'
                        )">
                      </Autocomplete>
                    </td>

                    <!-- date cell: -->
                    <td class="py-1 text-sm px-4 border-b activecell">
                      <div class="relative">
                        <div v-if="!task.planned_start_date"
                          class="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none"
                          style="z-index: 5;">
                          <CalendarIcon class="h-5 w-5" />
                        </div>
                        <DatePicker icon-left="calendar" :value="task.planned_start_date" placeholder=""
                          :disabled="false" :formatter="(date) => formatDate(date, 'DD-MMM-YYYY')" @change="
                            (data) =>
                              handleFieldChange(
                                index,
                                taskIndex,
                                'date',
                                data,
                                'planned_start_date'
                              )
                          " />
                      </div>
                    </td>

                    <td class="py-1 text-sm px-4 border-b activecell">
                      <div class="relative">
                        <div v-if="!task.planned_end_date"
                          class="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none"
                          style="z-index: 5;">
                          <CalendarIcon class="h-5 w-5" />
                        </div>
                        <DatePicker icon-left="calendar" :value="task.planned_end_date" placeholder="" :disabled="false"
                          :formatter="(date) => formatDate(date, 'DD-MMM-YYYY')" @change="
                            (data) =>
                              handleFieldChange(
                                index,
                                taskIndex,
                                'date',
                                data,
                                'planned_end_date'
                              )
                          " />
                      </div>
                    </td>

                    <td class="py-1 text-sm px-4 border-b activecell">
                      <div class="relative">
                        <div v-if="!task.actual_start_date"
                          class="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none"
                          style="z-index: 5;">
                          <CalendarIcon class="h-5 w-5" />
                        </div>
                        <DatePicker icon-left="calendar" :value="task.actual_start_date" placeholder=""
                          :disabled="false" :formatter="(date) => formatDate(date, 'DD-MMM-YYYY')" @change="
                            (data) =>
                              handleFieldChange(
                                index,
                                taskIndex,
                                'date',
                                data,
                                'actual_start_date'
                              )
                          " />

                      </div>
                    </td>

                    <td class="py-1 text-sm px-4 border-b activecell">
                      <div class="relative">
                        <div v-if="!task.actual_end_date"
                          class="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none"
                          style="z-index: 5;">
                          <CalendarIcon class="h-5 w-5" />
                        </div>
                        <DatePicker icon-left="calendar" :value="task.actual_end_date" placeholder="" :disabled="false"
                          :formatter="(date) => formatDate(date, 'DD-MMM-YYYY')" @change="
                            (data) =>
                              handleFieldChange(
                                index,
                                taskIndex,
                                'date',
                                data,
                                'actual_end_date'
                              )
                          " />
                      </div>
                    </td>

                    <td class="py-1 text-sm px-4 border-b activecell">
                      <div class="relative">
                        <span class="block truncate cursor-pointer" :style="{ width: columnWidths.remarks + 'px' }"
                          @click="
                            ; (showRemarksDialog = true),
                            (remarksRef = {
                              title: 'Remarks for ' + task.name,
                              index: index,
                              taskIndex: taskIndex,
                              remarks: task.remarks,
                              updatingTaskRemarks: true,
                              updatingSubTaskRemarks: false,
                            })
                            ">
                          {{ task.remarks ?? '-' }}
                        </span>
                      </div>
                    </td>

                    <td class="py-1 text-sm px-4 border-b"></td>
                  </tr>

                  <!-- Subtasks -->
                  <template v-if="task.isExpanded && task.subtasks?.length">
                    <tr v-for="(subtask, subtaskIndex) in task.subtasks" :key="`${taskIndex}-${subtaskIndex}`"
                      class="border-t border-gray-100 group hover:bg-[#F8F7F9]">

                      <td class="relative" style="position: sticky; left: 0px; z-index: 20;background: white;">
                        <!-- Background placeholder -->
                        <div class="absolute inset-0 bg-white"
                          :class="{ 'invisible': selectedTasks.includes(subtask.id) || isSubtaskHovered }"></div>

                        <!-- Interactive elements -->
                        <div class="text-sm py-3 flex items-center gap-2 transition duration-300 relative" :class="{
                          'opacity-100 translate-x-0': selectedTasks.includes(subtask.id),
                          'opacity-0 translate-x-1 group-hover:opacity-100 group-hover:translate-x-0': !selectedTasks.includes(subtask.id),
                        }" @mouseover="isSubtaskHovered = true" @mouseleave="isSubtaskHovered = false">
                          <span class="cursor-move">
                            <NineDotsIcon />
                          </span>
                          <input type="checkbox" class="border rounded-sm bg-[#CAC4D0]" v-model="selectedTasks"
                            :value="subtask.id" :disabled="hasSelectedMainTasks"
                            @mouseover="e => hasSelectedMainTasks && showTooltip(e, 'Cannot select subtask while main tasks are selected')"
                            @mouseleave="hideTooltip" />
                        </div>
                      </td>

                      <td class="py-1 text-sm px-4 pl-8 border-b bg-white group-hover:bg-[#F8F7F9]"
                        style="position: sticky; left: 50px; z-index: 10;">
                        <div class="flex items-center justify-between cursor-pointer">
                          <span v-if="subtask.isEditing">
                            <input v-model="subtask.name" ref="newSubtaskInput" v-focus v-select @blur="
                              handleSubtaskBlur(index, taskIndex, subtaskIndex)
                              " @keyup.enter="
                                finishEditingSubtask(
                                  index,
                                  taskIndex,
                                  subtaskIndex
                                )
                                " class="text-sm px-2 py-1 border-none outline-none bg-transparent"
                              placeholder="Enter Subtask Name" @click.stop />
                          </span>
                          <span v-else>
                            {{ subtask.name }}
                          </span>
                          <div v-if="!isEditingSubtask"
                            class="py-1 text-sm flex items-center opacity-0 translate-x-1 group-hover:opacity-100 group-hover:translate-x-0 transition duration-300">
                            <button @click.stop="startEditingSubtaskName(index, taskIndex, subtaskIndex)"
                              class="ml-2 text-gray-400 bg-gray-300 rounded-sm p-0.5">
                              <PencilIcon />
                            </button>
                          </div>
                        </div>
                      </td>

                      <!-- Subtask Owner cell -->
                      <td class="py-1 text-sm px-4 relative border-b activecell">
                        <Autocomplete :options="usersLists" v-model="subtask.assignees" placeholder="Select Assignee"
                          :multiple="true" :by="(a, b) => a.value === b.value">
                          <template #target="{ togglePopover }">
                            <div @click="togglePopover" class="cursor-pointer">
                              <span v-if="subtask.assignees?.length" class="flex -space-x-1">
                                <div v-for="(assignee, idx) in subtask.assignees" :key="idx"
                                  class="w-8 h-8 rounded-full bg-[#CCC2DC] flex items-center justify-center text-xs font-medium text-[#000] ring-1 ring-white">
                                  <Tooltip :text="assignee.label" :hover-delay="0" :placement="'top'">
                                    <span>
                                      {{ getInitialsFromFullName(assignee) }}
                                    </span>
                                  </Tooltip>
                                </div>
                              </span>
                              <span v-else
                                class="w-8 h-8 rounded-full bg-[#CCC2DC] flex items-center justify-center text-xs font-medium text-[#000] ring-1 ring-white">
                                <OwnerIcon />
                              </span>
                            </div>
                          </template>
                          <template #footer="{ togglePopover }">
                            <div class="flex items-center justify-end">
                              <Button variant="solid" @click="
                                handleSubtaskFieldChange(
                                  index,
                                  taskIndex,
                                  subtaskIndex,
                                  'owner',
                                  subtask.assignees,
                                  'task_assignee'
                                ),
                                togglePopover()
                                ">Update</Button>
                            </div>
                          </template>
                        </Autocomplete>
                      </td>

                      <!-- Subtask Status cell -->
                      <td class="py-1 text-sm px-4 border-b activecell">
                        <Autocomplete v-model="subtask.status" :placeholder="'Select Status'" :showOptions="true"
                          :options="statusList" @update:modelValue="handleSubtaskFieldChange(
                            index,
                            taskIndex,
                            subtaskIndex,
                            'status',
                            subtask.status,
                            'status'
                          )">
                        </Autocomplete>
                      </td>
                      <!-- Subtask Date cells -->
                      <td class="py-1 text-sm px-4 border-b activecell">
                        <div class="relative">
                          <div v-if="!subtask.planned_start_date"
                            class="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none"
                            style="z-index: 5;">
                            <CalendarIcon class="h-5 w-5 text-gray-700" />
                          </div>
                          <DatePicker icon-left="calendar" :value="subtask.planned_start_date" placeholder=""
                            :disabled="false" :formatter="(date) => formatDate(date, 'DD-MMM-YYYY')" @change="
                              (data) =>
                                handleSubtaskFieldChange(
                                  index,
                                  taskIndex,
                                  subtaskIndex,
                                  'date',
                                  data,
                                  'planned_start_date'
                                )
                            " />
                        </div>
                      </td>

                      <td class="py-1 text-sm px-4 border-b activecell">
                        <div class="relative">
                          <div v-if="!subtask.planned_end_date"
                            class="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none"
                            style="z-index: 5;">
                            <CalendarIcon class="h-5 w-5 text-gray-700" />
                          </div>
                          <DatePicker icon-left="calendar" :value="subtask.planned_end_date" placeholder=""
                            :disabled="false" :formatter="(date) => formatDate(date, 'DD-MMM-YYYY')" @change="
                              (data) =>
                                handleSubtaskFieldChange(
                                  index,
                                  taskIndex,
                                  subtaskIndex,
                                  'date',
                                  data,
                                  'planned_end_date'
                                )
                            " />
                        </div>
                      </td>

                      <td class="py-1 text-sm px-4 border-b activecell">
                        <div class="relative">
                          <div v-if="!subtask.actual_start_date"
                            class="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none"
                            style="z-index: 5;">
                            <CalendarIcon class="h-5 w-5 text-gray-700" />
                          </div>
                          <DatePicker icon-left="calendar" :value="subtask.actual_start_date" placeholder=""
                            :disabled="false" :formatter="(date) => formatDate(date, 'DD-MMM-YYYY')" @change="
                              (data) =>
                                handleSubtaskFieldChange(
                                  index,
                                  taskIndex,
                                  subtaskIndex,
                                  'date',
                                  data,
                                  'actual_start_date'
                                )
                            " />
                        </div>
                      </td>

                      <td class="py-1 text-sm px-4 border-b activecell">
                        <div class="relative">
                          <div v-if="!subtask.actual_end_date"
                            class="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none"
                            style="z-index: 5;">
                            <CalendarIcon class="h-5 w-5 text-gray-700" />
                          </div>
                          <DatePicker icon-left="calendar" :value="subtask.actual_end_date" placeholder=""
                            :disabled="false" :formatter="(date) => formatDate(date, 'DD-MMM-YYYY')" @change="

                              (data) =>
                                handleSubtaskFieldChange(
                                  index,
                                  taskIndex,
                                  subtaskIndex,
                                  'date',
                                  data,
                                  'actual_end_date'
                                )
                            " />
                        </div>
                      </td>

                      <td class="py-1 text-sm px-4 border-b activecell">
                        <div class="relative">
                          <span class="block truncate cursor-pointer" :style="{ width: columnWidths.remarks + 'px' }"
                            @click="
                              ; (showRemarksDialog = true),
                              (remarksRef = {
                                title: 'Remarks for ' + subtask.name,
                                index: index,
                                taskIndex: taskIndex,
                                subtaskIndex: subtaskIndex,
                                remarks: subtask.remarks,
                                updatingTaskRemarks: false,
                                updatingSubTaskRemarks: true,
                              })
                              ">
                            {{ subtask.remarks ?? '-' }}
                          </span>
                        </div>
                      </td>

                      <td class="py-1 text-sm px-4 border-b"></td>
                    </tr>
                  </template>
                </template>
              </tbody>
            </table>
          </div>
          <div v-if="!isEditingTask" class="mt-2 text-sm pt-1">
            <button class="flex items-center gap-6" @click="addTask(index)" style="position: sticky; left: 20px;">
              <PlusButtonIcon />
              <span class="ml-2 text-gray-900"> Add Task </span>
            </button>
          </div>
        </div>
      </div>
    </div>
    <div class="fixed bottom-2 left-48 z-50">
      <button @click="addSection" v-if="!isEditingSection && activeSubTab != 'null'"
        class="flex items-center gap-4 mt-4 mb-4 px-6 py-1 bg-gray-50 border shadow-md rounded-xl text-gray-700"
        style="position: sticky; left: 0px;">
        <AddSectionIcon />
        Add deliverables
      </button>
    </div>
    <!-- bottom line functionality  -->
    <div v-if="selectedTasks.length > 0"
      class="fixed z-50 bottom-4 left-1/2 transform -translate-x-1/2 flex items-center gap-2 bg-white rounded-xl shadow-lg border h-16">
      <div class="bg-[#EADDFF] h-full flex item-center w-14 rounded-tl-xl rounded-bl-xl">
        <span class="px-6 py-5 rounded-md text-md font-normal">
          {{ selectedTasks.length }}
        </span>
      </div>
      <div class="pr-5">
        <span class="text-md font-normal pl-5">Tasks Selected</span>
      </div>
      <div class="flex items-center gap-4">
        <button @click="handleDuplicate"
          class="flex flex-col items-center px-3 py-1 hover:bg-gray-100 rounded-md text-sm">
          <DuplicateIcon />
          Duplicate
        </button>
        <button @click="handleDelete"
          class="flex flex-col items-center gap-1 px-3 py-1 hover:bg-gray-100 rounded-md text-sm">
          <DeleteIcon />
          Delete
        </button>
        <Autocomplete :options="movableSections" @update:modelValue="(v) => handleMove(v)" placeholder="Select Section">
          <template #target="{ togglePopover }">
            <div @click="setMovableSections(), togglePopover()"
              class="flex flex-col items-center px-3 py-1 hover:bg-gray-100 rounded-md text-sm cursor-pointer ml-2">
              <MoveIcon />
              Move
            </div>
          </template>
        </Autocomplete>
      </div>
      <button @click="clearSelection" class="ml-2 p-4 hover:bg-gray-100 rounded-full">
        <CrossIcon />
      </button>
    </div>
  </div>

  <Dialog v-model="showRemarksDialog" class="z-[100]">
    <template #body-title>
      <h2 class="text-lg font-bold">{{ remarksRef.title }}</h2>
    </template>
    <template #body-content>
      <div class="p-1 bg-gray-100 rounded-md">
        <Textarea :ref_for="true" :variant="'subtle'" size="sm" placeholder="Remarks" :disabled="false"
          :modelValue="remarksRef.remarks" :rows="10" @change="(e) => (remarksRef.remarks = e.target.value)"></Textarea>
      </div>
    </template>
    <template #actions>
      <!-- <div class="flex flex-row gap-4">
        <button  class="py-5 w-full" @click="showRemarksDialog = false">
          Close
        </button>
        <button  @click="updateRemarks" class="py-5 w-full">
          Update
        </button>
      </div> -->
      <div class="flex justify-end gap-4">
        <button class="text-sm py-2 px-6 border border-gray-400 text-gray-700 rounded hover:bg-gray-100"
          @click="showRemarksDialog = false">
          Cancel
        </button>
        <button @click="updateRemarks" class="text-sm py-2 px-6 bg-black text-white rounded hover:bg-gray-800">
          Update
        </button>
      </div>
    </template>
  </Dialog>

  <div v-if="tooltip.show" class="fixed z-[1000] bg-gray-800 text-white text-xs px-2 py-1 rounded pointer-events-none"
    :style="{
      left: `${tooltip.x + 10}px`,
      top: `${tooltip.y + 10}px`
    }">
    {{ tooltip.message }}
  </div>
</template>
<script setup>
import { ref, nextTick, watch, h, computed, onBeforeUnmount, onMounted } from 'vue'
import {
  createResource,
  toast,
  Dialog,
  createListResource,
  Tooltip,
  Autocomplete,
  DatePicker,
  FeatherIcon,
  Textarea,
} from 'frappe-ui'
import ResizableHeader from './ResizableHeader.vue'

import CustomDropDown from './CustomDropDown.vue'
import PlusButtonIcon from './icons/PlusButtonIcon.vue'
import AddSectionIcon from './icons/AddSectionIcon.vue'
import DropDownIcon from './icons/DropDownIcon.vue'
import ThreeDots from './icons/ThreeDots.vue'
import SmallDropdownIcon from './icons/SmallDropdownIcon.vue'
import LinkageIcon from './icons/LinkageIcon.vue'
import AddSubTaskIcon from './icons/AddSubTaskIcon.vue'
import PencilIcon from './icons/PencilIcon.vue'
import OwnerIcon from './icons/OwnerIcon.vue'
import DuplicateIcon from './icons/DuplicateIcon.vue'
import DeleteIcon from './icons/DeleteIcon.vue'
import MoveIcon from './icons/MoveIcon.vue'
import ConvertIcon from './icons/ConvertIcon.vue'
import CrossIcon from './icons/CrossIcon.vue'
import NineDotsIcon from './icons/NineDotsIcon.vue'
import DuedateIcon from './icons/DuedateIcon.vue'
import CalendarIcon from './icons/CalendarIcon.vue'

import {
  frappeSetValue,
} from '../utils/frappeAPI'
import { formatDate } from '../utils/format'

const props = defineProps({
  activeSubTab: {
    type: String,
    required: true,
  },
  projectId: {
    type: String,
    required: true,
  },
})

let isLoading = ref(false)
const draggedTaskIndex = ref(null)
const sections = ref([])
const sectionNameInput = ref(null)
const editingTaskIndex = ref(null)
const editingSubtaskIndices = ref({ sectionIndex: null, taskIndex: null })
const newTaskInput = ref(null)
const newSubtaskInput = ref(null)
const statusOptions = [
  'Open',
  'Working',
  'Pending Review',
  'Overdue',
  'Completed',
]
const usersLists = ref([])
const statusList = ref([])
const selectedTasks = ref([])

const isEditingTask = ref(false)
const isEditingSection = ref(false)
const isEditingSubtask = ref(false)

// Contains a list of sections that tasks can be moved to
let movableSections = ref([])

let showRemarksDialog = ref(false)
let remarksRef = ref({
  title: null,
  index: null,
  taskIndex: null,
  subtaskIndex: null,
  remarks: null,
  updatingTaskRemarks: false,
  updatingSubTaskRemarks: false,
})

// Store initial widths
const columnWidths = ref({
  checkbox: 50,
  task: 400,
  owner: 220,
  status: 220,
  planned_start_date: 250,
  planned_end_date: 250,
  actual_start_date: 250,
  actual_end_date: 250,
  remarks: 100,
  actions: 60,
})

// Functions for saving and loading column preferences
const saveColumnPreferences = () => {
  const saveResource = createResource({
    url: 'inspira.inspira.api.projects.project_planner.save_column_preferences',
    makeParams: () => ({
      view_name: 'Planner',
      columns: JSON.stringify(columnWidths.value)
    }),
    onSuccess: () => {
      console.log('Column preferences saved successfully')
    },
    onError: (error) => {
      console.error('Failed to save column preferences:', error)
      toast({
        title: 'Error',
        text: 'Failed to save column preferences',
        icon: 'alert-circle',
        position: 'bottom-right',
        iconClasses: 'text-red-500',
      })
    }
  })
  saveResource.submit()
}

const loadColumnPreferences = () => {
  const loadResource = createResource({
    url: 'inspira.inspira.api.projects.project_planner.load_column_preferences',
    makeParams: () => ({
      view_name: 'Planner'
    }),
    auto: true,
    onSuccess: (data) => {
      if (data.status === 'success' && data.columns) {
        columnWidths.value = { ...columnWidths.value, ...data.columns }
        console.log('Column preferences loaded successfully')
      }
    },
    onError: (error) => {
      console.error('Failed to load column preferences:', error)
    }
  })
}

const vFocus = {
  mounted: (el) => el.focus(),
}

const vSelect = {
  mounted: (el) => el.select(),
}

//tooltip methods and ref
const tooltip = ref({
  show: false,
  message: '',
  x: 0,
  y: 0
})

const showTooltip = (event, message) => {
  tooltip.value = {
    show: true,
    message,
    x: event.clientX,
    y: event.clientY
  }
}

const hideTooltip = () => {
  tooltip.value.show = false
}

const get_sections = (activeSubTab) => {
  // Store expanded states for both sections and tasks
  const states = sections.value.reduce((acc, section) => {
    acc[section.id] = {
      sectionExpanded: section.isExpanded,
      tasks: section.tasks.reduce((taskAcc, task) => {
        taskAcc[task.id] = task.isExpanded
        return taskAcc
      }, {}),
    }
    return acc
  }, {})

  const ownerOfTaskResource = createResource({
    url: 'inspira.inspira.api.projects.project_planner.get_deliverables',
    makeParams: () => ({
      milestone_id: activeSubTab,
    }),
    auto: true,
    onSuccess: (data) => {
      data.forEach(section => section.isExpanded = true);
      data.forEach((section) => {
        if (states[section.id]) {
          section.isExpanded = states[section.id].sectionExpanded
          section.tasks?.forEach((task) => {
            if (states[section.id].tasks[task.id] !== undefined) {
              task.isExpanded = states[section.id].tasks[task.id]
            }
          })
        }
      })
      sections.value = data
    },
    onError: (error) => {
      console.error('Failed to fetch deliverables:', error)
    },
  })
}
watch(
  () => props.activeSubTab,
  (newValue) => {
    if (newValue) {
      get_sections(newValue)
    }
  },
  { immediate: true }
)

// Watch for column width changes and save preferences
watch(
  columnWidths,
  () => {
    // Debounce the save operation to avoid too many API calls
    clearTimeout(saveTimeout)
    saveTimeout = setTimeout(() => {
      saveColumnPreferences()
    }, 1000) // Save after 1 second of no changes
  },
  { deep: true }
)

// Timeout for debouncing save operations
let saveTimeout = null

// Load column preferences on component mount
onMounted(() => {
  loadColumnPreferences()
})
let fetchUsers = createListResource({
  doctype: 'IDP Project User',
  parent: 'IDP Project',
  fields: ['user as value', 'full_name as label'],
  orderBy: 'creation desc',
  start: 0,
  auto: 1,
  pageLength: 100,
  filters: {
    parent: props.projectId,
  },
  onSuccess(data) {
    console.log('Users list from milestone', data)
    usersLists.value = data
  },
})

createListResource({
  doctype: 'IDP Status Master',
  fields: ['name as value', 'status as label'],
  orderBy: 'creation desc',
  start: 0,
  auto: 1,
  pageLength: 100,
  filters: {
    form: "Task",
  },
  onSuccess(data) {
    statusList.value = data
  },
})

const withProcessingState = async (item, callback) => {
  if (item.isProcessing) return
  item.isProcessing = true
  try {
    await callback()
  } finally {
    item.isProcessing = false
  }
}

// Start Of Sections
const toggleSection = (index) => {
  sections.value[index].isExpanded = !sections.value[index].isExpanded
}

const addSection = () => {
  isEditingSection.value = true
  const newSection = {
    name: '',
    color: '#CCC2DC',
    isExpanded: false,
    isEditing: true,
    tasks: [],
  }
  sections.value.push(newSection)
}

const addNewSection = (newSectionIndex) => {
  isEditingSection.value = true
  const newSection = {
    name: '',
    color: '#CCC2DC',
    isExpanded: false,
    isEditing: true,
    tasks: [],
  }
  sections.value.splice(newSectionIndex, 0, newSection)
}

const handleSectionBlur = async (index) => {
  const section = sections.value[index]
  if (!section || section.name.trim() === '') {
    sections.value.splice(index, 1)
    isEditingSection.value = false
    return
  }

  const sectionLength = sections.value.length

  let bulkUpdateParams = []
  if (!(index === sectionLength)) {
    // This means section is being inserted at a specific index
    //  Need to update the indexes of the subsequent sections
    for (let i = index + 1; i < sectionLength; i++) {
      // sections[i].idx = i + 1;
      bulkUpdateParams.push({
        doctype: 'IDP Task',
        docname: sections.value[i].id,
        idx: i + 1,
      })
    }
  }

  // If not section id, create a new section, else update the existing one
  if (!section.id) {
    const sectionResource = createResource({
      url: 'frappe.client.insert',
      makeParams: () => ({
        doc: {
          doctype: 'IDP Task',
          idx: index + 1,
          parent_task: props.activeSubTab,
          subject: section.name,
          type: 'Deliverables',
          group: 'Planner',
          color: section.color,
          is_group: true,
          is_milestone: false,
          project: props.projectId,
        },
      }),
      auto: true,
      onSuccess: async () => {
        if (bulkUpdateParams.length) {
          let successMsg = 'Section Created Successfully'
          let errorMsg = 'Error while updating task order'
          const successFunc = () => {
            get_sections(props.activeSubTab)
          }
          frappeBulkUpdate(
            bulkUpdateParams,
            successFunc,
            null,
            successMsg,
            errorMsg
          )
        } else {
          get_sections(props.activeSubTab)
          toast({
            title: 'Success',
            text: 'Section created successfully',
            icon: 'check-circle',
            position: 'bottom-right',
            iconClasses: 'text-green-500',
          })
        }
        section.isEditing = false
        isEditingSection.value = false
      },
      onError: (error) => {
        sections.value.splice(index, 1)
        isEditingSection.value = false
        toast({
          title: 'Error',
          text: error.messages?.[0] || 'Failed to create Section',
          icon: 'alert-circle',
          position: 'bottom-right',
          iconClasses: 'text-red-500',
        })
      },
    })
  } else {
    const sectionResource = createResource({
      url: 'frappe.client.set_value',
      makeParams: () => ({
        doctype: 'IDP Task',
        name: section.id,
        fieldname: 'subject',
        value: section.name,
      }),
      auto: true,
      onSuccess: () => {
        section.isEditing = false
        isEditingSection.value = false
        get_sections(props.activeSubTab)
        toast({
          title: 'Success',
          text: 'Section updated successfully',
          icon: 'check-circle',
          position: 'bottom-right',
          iconClasses: 'text-green-500',
        })
      },
      onError: (error) => {
        sections.value.splice(index, 1)
        isEditingSection.value = false
        toast({
          title: 'Error',
          text: error.messages?.[0] || 'Failed to update Section',
          icon: 'alert-circle',
          position: 'bottom-right',
          iconClasses: 'text-red-500',
        })
      },
    })
  }
}

const finishEditingSection = async (index) => {
  const section = sections.value[index]
  await withProcessingState(section, async () => {
    if (section.name.trim() === '') {
      sections.value.splice(index, 1)
    } else {
      section.isEditing = false
    }
  })
}

// End Of Sections

// Start Of Main Task
const addTask = (sectionIndex) => {
  // if (isEditingTask.value) return
  isEditingTask.value = true
  const newTask = {
    name: '',
    status: '',
    owner: '',
    date: '',
    isEditing: true,
    isEditingOwner: true,
    isEditingStatus: true,
    isEditingDate: true,
    isExpanded: false,
  }
  sections.value[sectionIndex].tasks.push(newTask)
  editingTaskIndex.value = sections.value[sectionIndex].tasks.length - 1

  nextTick(() => {
    if (newTaskInput.value) {
      newTaskInput.value.focus()
    }
  })
}

const handleTaskBlur = (sectionIndex, taskIndex) => {
  const task = sections.value[sectionIndex].tasks[taskIndex]

  if (!task || task.name.trim() === '') {
    sections.value[sectionIndex].tasks.splice(taskIndex, 1)
    isEditingTask.value = false
    return
  }

  finishEditingTask(sectionIndex, taskIndex)
}

const finishEditingTask = (sectionIndex, taskIndex) => {
  const task = sections.value[sectionIndex].tasks[taskIndex]

  if (task.isProcessing) return // Prevent duplicate processing
  task.isProcessing = true

  if (task.name.trim() === '') {
    sections.value[sectionIndex].tasks.splice(taskIndex, 1)
    task.isProcessing = false
    return
  } else {
    task.isEditing = false
    // If task already has an ID, update it instead of creating new
    if (task.id) {
      const taskResource = createResource({
        url: 'frappe.client.set_value',
        makeParams: () => ({
          doctype: 'IDP Task',
          name: task.id,
          fieldname: 'subject',
          value: task.name,
        }),
        auto: true,
        onSuccess: () => {
          toast({
            title: 'Success',
            text: 'Task updated successfully',
            icon: 'check-circle',
            position: 'bottom-right',
            iconClasses: 'text-green-500',
          })
        },
        onError: (error) => {
          task.isProcessing = false // Reset flag
          toast({
            title: 'Error',
            text: error.messages?.[0] || 'Failed to update task',
            icon: 'alert-circle',
            position: 'bottom-right',
            iconClasses: 'text-red-500',
          })
        },
      })
    } else {
      const idx = sections.value[sectionIndex].tasks.length
      const taskResource = createResource({
        url: 'frappe.client.insert',
        makeParams: () => ({
          doc: {
            doctype: 'IDP Task',
            idx: idx,
            project: props.projectId,
            parent_task: sections.value[sectionIndex].id,
            subject: task.name,
            task_assignee: task.owner,
            type: 'Task',
            group: 'Planner',
            is_group: false,
            is_milestone: false,
            planned_start_date: task.planned_start_date,
            planned_end_date: task.planned_end_date,
            actual_start_date: task.actual_start_date,
            actual_end_date: task.actual_end_date,
          },
        }),
        auto: true,
        onSuccess: (data) => {
          sections.value[sectionIndex].tasks[taskIndex].id = data.name
          sections.value[sectionIndex].tasks[taskIndex].status = data.status
          task.isProcessing = false
          toast({
            title: 'Success',
            text: 'Task created successfully',
            icon: 'check-circle',
            position: 'bottom-right',
            iconClasses: 'text-green-500',
          })
        },
        onError: (error) => {
          console.error('Failed to create task:', error)
          sections.value[sectionIndex].tasks.splice(taskIndex, 1)
          task.isProcessing = false
          toast({
            title: 'Error',
            text: error.messages?.[0] || 'Failed to create task',
            icon: 'alert-circle',
            position: 'bottom-right',
            iconClasses: 'text-red-500',
          })
        },
      })
    }
  }
  editingTaskIndex.value = null
  isEditingTask.value = false
}

const startEditing = (sectionIndex, taskIndex, field) => {
  const task = sections.value[sectionIndex].tasks[taskIndex]
  task[`isEditing${field.charAt(0).toUpperCase() + field.slice(1)}`] = true
}

const updateRemarks = () => {
  if (remarksRef.value.updatingTaskRemarks) {
    handleFieldChange(
      remarksRef.value.index,
      remarksRef.value.taskIndex,
      'remarks',
      remarksRef.value.remarks,
      'remarks'
    )
  } else {
    handleSubtaskFieldChange(
      remarksRef.value.index,
      remarksRef.value.taskIndex,
      remarksRef.value.subtaskIndex,
      'remarks',
      remarksRef.value.remarks,
      'remarks'
    )
  }
  showRemarksDialog.value = false
}

const handleFieldChange = async (
  sectionIndex,
  taskIndex,
  field,
  value,
  fieldname
) => {
  const task = sections.value[sectionIndex].tasks[taskIndex]
  await withProcessingState(task, async () => {
    task[`isEditing${field.charAt(0).toUpperCase() + field.slice(1)}`] = false

    if (field == 'owner') {
      let newValue = []
      value.forEach((user) => {
        newValue.push({
          doctype: 'IDP Task Assignee',
          user: user.value,
          user_name: user.label,
        })
      })
      value = newValue
    }

    if (field === 'status') {
      value = value['value']
    }
    const setValueParams = {
      doctype: 'IDP Task',
      name: task.id,
      fieldname: fieldname,
      value: value,
    }

    const successFunc = async () => {
      if (!props.activeSubTab) {
        console.error(
          '❌ props.activeSubTab is undefined! Cannot fetch sections.'
        )
        return
      }
      get_sections(props.activeSubTab)
      await nextTick()
      sections.value = [...sections.value]
    }

    const successMsg = `Task ${field} updated`
    const errorMsg = `Failed to update ${field}`

    const errorFunc = (error) => {
      get_sections(props.activeSubTab)
    }
    frappeSetValue({
      setValueParams: setValueParams,
      successFunc: successFunc,
      successMsg: successMsg,
      errorMsg: errorMsg,
      errorFunc: errorFunc,
    })

    await nextTick()
    console.log('👀 Waiting for UI update...')
  })
}

const startEditingTaskName = (sectionIndex, taskIndex) => {
  sections.value[sectionIndex].tasks[taskIndex].isEditing = true
  nextTick(() => {
    if (newTaskInput.value) {
      newTaskInput.value.focus()
    }
  })
}
// End Of Main Task

// Start Of Subtask
const addSubtask = (sectionIndex, taskIndex) => {
  isEditingSubtask.value = true

  sections.value[sectionIndex].tasks[taskIndex].isExpanded = true
  if (!sections.value[sectionIndex].tasks[taskIndex].subtasks) {
    sections.value[sectionIndex].tasks[taskIndex].subtasks = []
  }
  sections.value[sectionIndex].tasks[taskIndex].subtasks.push({
    name: '',
    status: '',
    owner: '',
    date: '',
    isEditing: true,
    isEditingOwner: true,
    isEditingStatus: true,
    isEditingDate: true,
  })
  editingSubtaskIndices.value = { sectionIndex, taskIndex }
  nextTick(() => {
    newSubtaskInput.value?.focus()
  })
}

const handleSubtaskBlur = (sectionIndex, taskIndex, subtaskIndex) => {
  const subtask =
    sections.value[sectionIndex].tasks[taskIndex].subtasks[subtaskIndex]
  if (subtask.name.trim() === '') {
    sections.value[sectionIndex].tasks[taskIndex].subtasks.splice(
      subtaskIndex,
      1
    )
    isEditingSubtask.value = false
    return
  }
  finishEditingSubtask(sectionIndex, taskIndex, subtaskIndex)
}

const finishEditingSubtask = async (sectionIndex, taskIndex, subtaskIndex) => {
  const subtask = sections.value[sectionIndex].tasks[taskIndex].subtasks[subtaskIndex];
  await withProcessingState(subtask, async () => {
    if (subtask.name.trim() === '') {
      sections.value[sectionIndex].tasks[taskIndex].subtasks.splice(subtaskIndex, 1);
    } else {
      subtask.isEditing = false;

      // If subtask already has an ID, update it instead of creating new
      if (subtask.id) {
        const subtaskResource = createResource({
          url: 'frappe.client.set_value',
          makeParams: () => ({
            doctype: 'IDP Task',
            name: subtask.id,
            fieldname: 'subject',
            value: subtask.name,
          }),
          auto: true,
          onSuccess: () => {
            toast({
              title: 'Success',
              text: 'Subtask updated successfully',
              icon: 'check-circle',
              position: 'bottom-right',
              iconClasses: 'text-green-500',
            });
          },
          onError: (error) => {
            toast({
              title: 'Error',
              text: error.messages?.[0] || 'Failed to update subtask',
              icon: 'alert-circle',
              position: 'bottom-right',
              iconClasses: 'text-red-500',
            });
          },
        });
      } else {
        // This part is for new subtasks (shouldn't happen during editing)
        const idx = sections.value[sectionIndex].tasks[taskIndex].subtasks.length;
        const subtaskResource = createResource({
          url: 'frappe.client.insert',
          makeParams: () => ({
            doc: {
              doctype: 'IDP Task',
              idx: idx,
              project: props.projectId,
              parent_task: sections.value[sectionIndex].tasks[taskIndex].id,
              subject: subtask.name,
              task_assignee: subtask.owner,
              type: 'Sub Task',
              group: 'Planner',
              is_group: false,
              is_milestone: false,
              planned_start_date: subtask.planned_start_date,
              planned_end_date: subtask.planned_end_date,
              actual_start_date: subtask.actual_start_date,
              actual_end_date: subtask.actual_end_date,
            },
          }),
          auto: true,
          onSuccess: (data) => {
            sections.value[sectionIndex].tasks[taskIndex].subtasks[subtaskIndex].id = data.name;
            sections.value[sectionIndex].tasks[taskIndex].subtasks[subtaskIndex].status = data.status;
            toast({
              title: 'Success',
              text: 'Subtask created successfully',
              icon: 'check-circle',
              position: 'bottom-right',
              iconClasses: 'text-green-500',
            });
          },
          onError: (error) => {
            sections.value[sectionIndex].tasks[taskIndex].subtasks.splice(subtaskIndex, 1);
            toast({
              title: 'Error',
              text: error.messages?.[0] || 'Failed to create subtask',
              icon: 'alert-circle',
              position: 'bottom-right',
              iconClasses: 'text-red-500',
            });
          },
        });
      }
    }
    editingSubtaskIndices.value = { sectionIndex: null, taskIndex: null };
  });
  isEditingSubtask.value = false;
};

const startEditingSubtask = (sectionIndex, taskIndex, subtaskIndex, field) => {
  const subtask =
    sections.value[sectionIndex].tasks[taskIndex].subtasks[subtaskIndex]
  subtask[`isEditing${field.charAt(0).toUpperCase() + field.slice(1)}`] = true
}
const handleSubtaskFieldChange = async (
  sectionIndex,
  taskIndex,
  subtaskIndex,
  field,
  value,
  fieldname
) => {
  const subtask =
    sections.value[sectionIndex].tasks[taskIndex].subtasks[subtaskIndex]
  await withProcessingState(subtask, async () => {
    subtask[
      `isEditing${field.charAt(0).toUpperCase() + field.slice(1)}`
    ] = false

    if (field === 'owner') {
      let newValue = []
      value.forEach((user) => {
        newValue.push({
          doctype: 'IDP Task Assignee',
          user: user.value,
          user_name: user.label,
        })
      })
      value = newValue
    }

    if (field === 'status') {
      value = value['value']
    }

    const setValueParams = {
      doctype: 'IDP Task',
      name: subtask.id,
      fieldname: fieldname,
      value: value,
    }

    const successMsg = `Subtask ${field} updated`
    const successFunc = async () => {
      if (!props.activeSubTab) {
        return
      }
      get_sections(props.activeSubTab)
      await nextTick()
      sections.value = [...sections.value]
    }

    const errorMsg = `Failed to update ${field}`
    const errorFunc = (error) => {
      get_sections(props.activeSubTab)
    }

    frappeSetValue({
      setValueParams: setValueParams,
      successFunc: successFunc,
      successMsg: successMsg,
      errorMsg: errorMsg,
      errorFunc: errorFunc,
    })
  })
}

const startEditingSubtaskName = (sectionIndex, taskIndex, subtaskIndex) => {
  sections.value[sectionIndex].tasks[taskIndex].subtasks[subtaskIndex].isEditing = true;
  nextTick(() => {
    if (newSubtaskInput.value) {
      newSubtaskInput.value.focus();
    }
  });
};

// checking if main task or sub task is being selected or not
const hasSelectedMainTasks = computed(() => {
  return selectedTasks.value.some(taskId => {
    return sections.value.some(section =>
      section.tasks.some(task => task.id === taskId)
    )
  })
})

const hasSelectedSubtasks = computed(() => {
  return selectedTasks.value.some(taskId => {
    return sections.value.some(section =>
      section.tasks.some(task =>
        task.subtasks?.some(subtask => subtask.id === taskId)
      )
    )
  })
})


// Start of Bottom Functionality
const remove_tasks_from_ui = () => {
  sections.value = sections.value.map((section) => {
    const filteredTasks = section.tasks.filter(
      (task) => !selectedTasks.value.includes(task.id)
    )
    const tasksWithFilteredSubtasks = filteredTasks.map((task) => {
      if (task.subtasks) {
        return {
          ...task,
          subtasks: task.subtasks.filter(
            (subtask) => !selectedTasks.value.includes(subtask.id)
          ),
        }
      }
      return task
    })

    return {
      ...section,
      tasks: tasksWithFilteredSubtasks,
    }
  })
}
const handleDelete = () => {
  console.log('Deleted tasks with IDs:', selectedTasks.value)
  deleteTaskWithChildren(selectedTasks.value)
}

const deleteTaskWithChildren = (taskIds, index = null) => {
  let deleteTasks = createResource({
    url: '/api/method/inspira.inspira.api.projects.project_planner.delete_tasks_with_children',
    params: {
      task_ids: taskIds,
    },
    auto: true,
    onSuccess(data) {
      // Required for removing the section from UI
      if (index != null) {
        sections.value.splice(index, 1)
      } else {
        remove_tasks_from_ui()
        selectedTasks.value = []
      }
      toast({
        title: 'Success',
        text: 'Deletion Successful',
        icon: 'check-circle',
        position: 'bottom-right',
        iconClasses: 'text-green-500',
      })
    },
    onError(err) {
      toast({
        title: 'Error Occurred',
        text: err.messages?.[0] || 'An unknown error occurred',
        icon: 'alert-circle',
        position: 'bottom-right',
        iconClasses: 'text-red-500',
      })
    },
  })
}

const handleDuplicate = () => {
  duplicateTaskWithChildren(selectedTasks.value)
  selectedTasks.value = []
}

const duplicateTaskWithChildren = (taskIds) => {
  createResource({
    url: '/api/method/inspira.inspira.api.projects.project_planner.duplicate_tasks_with_children',
    params: {
      task_ids: taskIds,
    },
    auto: true,
    onSuccess(data) {
      get_sections(props.activeSubTab)
      toast({
        title: 'Success',
        text: 'Task duplicated successfully',
        icon: 'check-circle',
        position: 'bottom-right',
        iconClasses: 'text-green-500',
      })
    },
    onError(err) {
      toast({
        title: 'Error Occurred',
        text: err.messages?.[0] || 'An unknown error occurred',
        icon: 'alert-circle',
        position: 'bottom-right',
        iconClasses: 'text-red-500',
      })
    },
  })
}

const getDeliverableTaskId = (taskId) => {
  for (const section of sections.value) {
    for (const task of section.tasks) {
      // Check if the task itself is the target
      if (task.id === taskId) {
        return section.id
      }

      // Search within subtasks
      for (const subtask of task.subtasks) {
        if (subtask.id === taskId) {
          return section.id
        }
      }
    }
  }
  return null
}

function getTasks() {
  // Function to return only tasks from tasks key, i.e remove subtasks if any
  const matchingTasks = []

  sections.value.forEach((section) => {
    section.tasks.forEach((task) => {
      if (selectedTasks.value.includes(task.id)) {
        matchingTasks.push(task)
      }
    })
  })

  return matchingTasks
}

function setMovableSections() {
  let deliverableID = new Set()
  selectedTasks.value.forEach((task) => {
    deliverableID.add(getDeliverableTaskId(task))
  })

  movableSections.value = sections.value
    .filter((task) => !deliverableID.has(task.id))
    .map((task) => ({
      label: task.name,
      value: task.id,
    }))
}

const handleMove = (newParent) => {
  let bulkUpdateParams = []
  const finalTasks = getTasks()

  finalTasks.forEach((task) => {
    bulkUpdateParams.push({
      doctype: 'IDP Task',
      docname: task.id,
      parent_task: newParent.value,
    })
  })

  createResource({
    url: 'inspira.inspira.api.projects.project_planner.move_tasks',
    makeParams: () => ({
      tasks: bulkUpdateParams,
    }),
    auto: true,
    onSuccess: (data) => {
      get_sections(props.activeSubTab)
      selectedTasks.value = []
    },
    onError: (error) => {
      console.error('Failed to move task:', error)
    },
  })
}

function frappeBulkUpdate(
  bulkUpdateParams,
  successFunc,
  errorFunction,
  successMsg,
  errorMsg
) {
  const resBulkUpdate = createResource({
    url: 'frappe.client.bulk_update',
    onSuccess(doc) {
      if (successFunc) {
        successFunc()
      }
      if (doc.failed_docs.length) {
        toast({
          title: 'Error',
          text: errorMsg,
          icon: 'alert-circle',
          position: 'bottom-right',
          iconClasses: 'text-red-500',
        })
      } else {
        toast({
          title: 'Success',
          text: successMsg,
          icon: 'check-circle',
          position: 'bottom-right',
          iconClasses: 'text-green-500',
        })
      }
    },
    onError(err) {
      if (errorFunction) {
        errorFunction()
      }

      toast({
        title: 'Error',
        text: err.messages?.[0] || errorMsg,
        icon: 'alert-circle',
        position: 'bottom-right',
        iconClasses: 'text-red-500',
      })
    },
  })

  resBulkUpdate.submit({
    docs: JSON.stringify(bulkUpdateParams),
  })
}

const handleConvert = () => { }

const clearSelection = () => {
  selectedTasks.value = []
}

const dragStart = (event, sectionIndex, taskIndex) => {
  event.dataTransfer.setData(
    'text/plain',
    JSON.stringify({ sectionIndex, taskIndex })
  )
  event.dataTransfer.effectAllowed = 'move'
}

const drop = (event, targetSectionIndex, targetTaskIndex) => {
  event.preventDefault()
  const data = JSON.parse(event.dataTransfer.getData('text/plain'))
  const { sectionIndex: sourceSectionIndex, taskIndex: sourceTaskIndex } = data

  const sourceSection = sections.value[sourceSectionIndex]
  const targetSection = sections.value[targetSectionIndex]
  const changedTasks = [] // To track tasks with new idx and sectionIndex

  if (
    sourceSectionIndex === targetSectionIndex &&
    sourceTaskIndex === targetTaskIndex
  )
    return

  // Helper to reindex tasks and track changes
  const reindexTasks = (section) => {
    section.tasks.forEach((task, index) => {
      const newIdx = index + 1 // idx starts at 1
      if (task.idx !== newIdx) {
        changedTasks.push({
          doctype: 'IDP Task',
          docname: task.id,
          idx: newIdx,
          parent_task: section.id,
        })
      }
      task.idx = newIdx
    })
  }

  const [movedTask] = sourceSection.tasks.splice(sourceTaskIndex, 1)

  if (sourceSectionIndex === targetSectionIndex) {
    sourceSection.tasks.splice(targetTaskIndex, 0, movedTask)
    reindexTasks(sourceSection)
  } else {
    // Different sections: move task and reindex both sections
    targetSection.tasks.splice(targetTaskIndex, 0, movedTask)
    reindexTasks(sourceSection)
    reindexTasks(targetSection)
  }

  // Ensure moved task is tracked
  if (!changedTasks.some((t) => t.docname === movedTask.id)) {
    changedTasks.push({
      doctype: 'IDP Task',
      docname: movedTask.id,
      idx: targetTaskIndex + 1, // Reflect its new position
      parent_task: targetSection.id,
    })
  }

  const successFunc = () => {
    // get_sections(props.activeSubTab)
    selectedTasks.value = []
  }

  let successMsg = 'Task Moved Successfully'
  let errorMsg = 'Error while moving tasks'
  frappeBulkUpdate(changedTasks, successFunc, null, successMsg, errorMsg)
}

const dragOver = (event, targetSectionIndex, targetTaskIndex) => {
  event.preventDefault()
  const rows = event.currentTarget.parentElement.children
  for (let i = 0; i < rows.length; i++) {
    rows[i].classList.remove('drag-over')
  }
  event.currentTarget.classList.add('drag-over')
}

const dragLeave = (event) => {
  event.currentTarget.classList.remove('drag-over')
}

// Add helper function
const getInitialsFromFullName = (assignee) => {
  let name = assignee?.label
  if (!name) return ''

  return name
    .split(' ')
    .filter((word) => word.length)
    .map((word) => word[0].toUpperCase())
    .join('')
}

const formatDateDDMMMYYYY = (dateString) => {
  if (!dateString) return ''
  const [day, month, year] = dateString.split('-').map(Number)
  const date = new Date(year, month - 1, day)
  if (isNaN(date.getTime())) return dateString
  const formattedDay = date.getDate().toString().padStart(2, '0')
  const formattedMonth = date.toLocaleString('default', { month: 'short' })
  const formattedYear = date.getFullYear()
  return `${formattedDay}-${formattedMonth}-${formattedYear}`
}

// Cleanup timeout on component unmount
onBeforeUnmount(() => {
  if (saveTimeout) {
    clearTimeout(saveTimeout)
  }
})
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.transform {
  transition: transform 0.3s ease-in-out;
}

.rotate-this {
  transform: rotate(270deg);
}

input[type='checkbox'] {
  outline: none;
  box-shadow: none;
}

input[type='checkbox']:checked {
  background-color: #4a4458;
  border-color: white;
}
</style>

<style>
.resizable-table {
  table-layout: fixed;
  min-width: fit-content;
  border-collapse: separate;
  border-spacing: 0;
}

body.resizing {
  cursor: col-resize !important;
  user-select: none !important;
}

.resizable-column>* {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.resizable-column:hover {
  background-color: #c8c2d1;
  cursor: pointer;
}

.activecell:hover {
  border: 1px solid rgb(212, 212, 212);
  border-radius: 0.25rem;
}

.resizable-table td:nth-child(2) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 0;
}

/* Keep other columns normal */
.resizable-table td:not(:nth-child(2)) {
  white-space: normal;
  max-width: none;
}
</style>

<style scoped>
select {
  border: none;
  outline: none;
  background: transparent;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  padding: 5px 30px 5px 10px;
}

select:focus {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

select {
  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='gray'><path fill-rule='evenodd' d='M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z' clip-rule='evenodd'/></svg>");
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 16px;
}

.custom-date-input {
  border: none;
  /* Removes border */
  outline: none;
  /* Removes focus outline */
  background: transparent;
  /* Keeps background clean */
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  padding: 5px 10px;
  /* Adjust padding */
  font-size: 14px;
  /* Adjust text size */
  cursor: pointer;
  /* Ensures pointer cursor */
}

/* Remove focus border and outline */
.custom-date-input:focus {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* Ensures the default calendar icon remains */
.custom-date-input::-webkit-calendar-picker-indicator {
  filter: invert(40%);
  cursor: pointer;
}

input[type="checkbox"]:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

::v-deep .w-full {
  background-color: transparent;
  border: transparent;
}

::v-deep .w-full button {
  background-color: transparent;
}
</style>
<style scoped>
.resizable-table {
  min-width: 100%;
}

.overflow-auto {
  scrollbar-width: thin;
}
</style>